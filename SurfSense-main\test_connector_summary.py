#!/usr/bin/env python3
"""
Test script to verify that connector service returns content_summary field
"""

import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'surfsense_backend'))

from app.utils.connector_service import ConnectorService
from app.db import async_session_maker

async def test_connector_summary():
    """Test that connector service returns content_summary field"""
    
    async with async_session_maker() as session:
        connector_service = ConnectorService(session, user_id="test-user")
        
        # Test search_files method
        try:
            result, chunks = await connector_service.search_files(
                user_query="test query",
                user_id="test-user", 
                search_space_id=1,
                top_k=5
            )
            
            print("✅ search_files executed successfully")
            print(f"Result structure: {list(result.keys())}")
            
            if result.get('sources'):
                first_source = result['sources'][0]
                print(f"First source keys: {list(first_source.keys())}")
                
                if 'content_summary' in first_source:
                    print("✅ content_summary field is present")
                    print(f"Summary preview: {first_source['content_summary'][:100]}...")
                else:
                    print("❌ content_summary field is missing")
            else:
                print("ℹ️  No sources returned (this is normal if no documents exist)")
                
        except Exception as e:
            print(f"❌ Error testing search_files: {e}")

if __name__ == "__main__":
    asyncio.run(test_connector_summary())
