"""
Tests and demonstrations for context optimization to solve "Lost in the Middle" problem.
"""

import pytest
from typing import List, Dict, Any
from app.agents.researcher.context_optimizer import (
    ContextOptimizer, 
    DocumentPlacementStrategy,
    create_context_optimizer,
    create_model_optimized_context_optimizer
)
from app.config.context_config import context_config


def create_mock_documents(count: int = 10) -> List[Dict[str, Any]]:
    """Create mock documents with varying relevance scores."""
    documents = []
    
    for i in range(count):
        # Create documents with decreasing relevance scores
        relevance_score = 1.0 - (i * 0.1)  # 1.0, 0.9, 0.8, etc.
        
        document = {
            "content": f"This is document {i+1} content. " * 20,  # ~20 words each
            "score": relevance_score,
            "chunk_id": f"chunk_{i+1}",
            "document": {
                "id": f"doc_{i+1}",
                "title": f"Document {i+1}",
                "document_type": "CRAWLED_URL"
            }
        }
        documents.append(document)
    
    return documents


class TestContextOptimizer:
    """Test context optimization strategies."""
    
    def test_relevance_first_strategy(self):
        """Test relevance-first placement (default behavior)."""
        documents = create_mock_documents(5)
        optimizer = create_context_optimizer("relevance_first")
        
        optimized_docs, info = optimizer.optimize_document_order(
            documents, 
            available_tokens=2000,
            query="test query"
        )
        
        # Should maintain relevance order
        scores = [doc.get("score", 0) for doc in optimized_docs]
        assert scores == sorted(scores, reverse=True)
        assert info["strategy"] == "relevance_first"
    
    def test_relevance_last_strategy(self):
        """Test relevance-last placement (recency bias)."""
        documents = create_mock_documents(5)
        optimizer = create_context_optimizer("relevance_last")
        
        optimized_docs, info = optimizer.optimize_document_order(
            documents, 
            available_tokens=2000,
            query="test query"
        )
        
        # Should have lowest relevance first, highest last
        scores = [doc.get("score", 0) for doc in optimized_docs]
        assert scores == sorted(scores)  # Ascending order
        assert info["strategy"] == "relevance_last"
    
    def test_sandwich_strategy(self):
        """Test sandwich placement (high relevance at start and end)."""
        documents = create_mock_documents(6)
        optimizer = create_context_optimizer("sandwich")
        
        optimized_docs, info = optimizer.optimize_document_order(
            documents, 
            available_tokens=2000,
            query="test query"
        )
        
        if len(optimized_docs) >= 3:
            # First document should have high relevance
            first_score = optimized_docs[0].get("score", 0)
            # Last document should have high relevance
            last_score = optimized_docs[-1].get("score", 0)
            # Middle documents should have lower relevance
            middle_scores = [doc.get("score", 0) for doc in optimized_docs[1:-1]]
            
            assert first_score >= 0.8  # High relevance
            assert last_score >= 0.7   # High relevance
            if middle_scores:
                assert max(middle_scores) <= first_score
        
        assert info["strategy"] == "sandwich"
    
    def test_attention_optimized_strategy(self):
        """Test attention-optimized placement."""
        documents = create_mock_documents(8)
        optimizer = create_context_optimizer("attention_optimized")
        
        optimized_docs, info = optimizer.optimize_document_order(
            documents, 
            available_tokens=2000,
            query="test query"
        )
        
        # Should optimize based on attention weights
        assert len(optimized_docs) > 0
        assert info["strategy"] == "attention_optimized"
        
        # Verify that high-relevance documents are not all in the middle
        if len(optimized_docs) >= 5:
            scores = [doc.get("score", 0) for doc in optimized_docs]
            
            # Check that highest scoring documents are not concentrated in middle positions
            middle_start = len(scores) // 3
            middle_end = 2 * len(scores) // 3
            middle_scores = scores[middle_start:middle_end]
            edge_scores = scores[:middle_start] + scores[middle_end:]
            
            if middle_scores and edge_scores:
                # Edge positions should have at least some high-scoring documents
                assert max(edge_scores) >= max(middle_scores) * 0.8
    
    def test_token_budget_constraint(self):
        """Test that optimizer respects token budget."""
        documents = create_mock_documents(20)  # Many documents
        optimizer = create_context_optimizer("attention_optimized")
        
        # Very limited token budget
        optimized_docs, info = optimizer.optimize_document_order(
            documents, 
            available_tokens=500,  # Small budget
            query="test query"
        )
        
        # Should select fewer documents
        assert len(optimized_docs) < len(documents)
        assert info["tokens_used"] <= 500
    
    def test_model_specific_optimization(self):
        """Test model-specific optimization."""
        documents = create_mock_documents(6)
        
        # Test GPT model (should prefer sandwich strategy)
        gpt_optimizer = create_model_optimized_context_optimizer("gpt-4")
        gpt_docs, gpt_info = gpt_optimizer.optimize_document_order(
            documents, available_tokens=2000, query="test"
        )
        
        # Test Claude model (should prefer relevance_first)
        claude_optimizer = create_model_optimized_context_optimizer("claude-3-sonnet")
        claude_docs, claude_info = claude_optimizer.optimize_document_order(
            documents, available_tokens=2000, query="test"
        )
        
        # Different models should potentially use different strategies
        # (depending on configuration)
        assert len(gpt_docs) > 0
        assert len(claude_docs) > 0
    
    def test_empty_documents(self):
        """Test handling of empty document list."""
        optimizer = create_context_optimizer("attention_optimized")
        
        optimized_docs, info = optimizer.optimize_document_order(
            [], available_tokens=2000, query="test"
        )
        
        assert optimized_docs == []
        assert info["total_documents"] == 0


class TestContextConfig:
    """Test context configuration."""
    
    def test_config_loading(self):
        """Test that configuration loads properly."""
        config = context_config
        
        assert config is not None
        assert hasattr(config, 'strategy')
        assert hasattr(config, 'enabled')
        assert hasattr(config, 'attention_weights')
    
    def test_model_specific_configs(self):
        """Test model-specific configurations."""
        config = context_config
        
        # Test GPT model config
        gpt_strategy = config.get_strategy_for_model("gpt-4")
        gpt_buffer = config.get_output_buffer_for_model("gpt-4")
        gpt_weights = config.get_attention_weights_for_model("gpt-4")
        
        assert isinstance(gpt_strategy, str)
        assert isinstance(gpt_buffer, float)
        assert isinstance(gpt_weights, dict)
        
        # Test Claude model config
        claude_strategy = config.get_strategy_for_model("claude-3-sonnet")
        assert isinstance(claude_strategy, str)
    
    def test_config_summary(self):
        """Test configuration summary."""
        config = context_config
        summary = config.get_config_summary()
        
        assert "enabled" in summary
        assert "strategy" in summary
        assert "attention_weights" in summary
        assert "available_strategies" in summary


def demo_context_optimization():
    """
    Demonstration of different context optimization strategies.
    Run this to see how different strategies arrange documents.
    """
    print("=== Context Optimization Demo ===\n")
    
    # Create sample documents
    documents = create_mock_documents(8)
    
    print("Original documents (by relevance score):")
    for i, doc in enumerate(documents):
        score = doc.get("score", 0)
        doc_id = doc["document"]["id"]
        print(f"  {i+1}. {doc_id} (score: {score:.1f})")
    
    print("\n" + "="*50)
    
    strategies = [
        "relevance_first",
        "relevance_last", 
        "sandwich",
        "interleaved",
        "attention_optimized"
    ]
    
    for strategy in strategies:
        print(f"\n--- {strategy.upper()} Strategy ---")
        
        optimizer = create_context_optimizer(strategy)
        optimized_docs, info = optimizer.optimize_document_order(
            documents, 
            available_tokens=2000,
            query="sample query about technology"
        )
        
        print("Document order:")
        for i, doc in enumerate(optimized_docs):
            score = doc.get("score", 0)
            doc_id = doc["document"]["id"]
            print(f"  {i+1}. {doc_id} (score: {score:.1f})")
        
        print(f"Strategy: {info['strategy']}")
        print(f"Documents used: {info['total_documents']}/{info['original_count']}")
        print(f"Tokens used: {info['tokens_used']:.0f}")
    
    print("\n" + "="*50)
    print("\nModel-specific optimizations:")
    
    models = ["gpt-4", "claude-3-sonnet", "gemini-pro"]
    for model in models:
        print(f"\n--- {model.upper()} ---")
        optimizer = create_model_optimized_context_optimizer(model)
        optimized_docs, info = optimizer.optimize_document_order(
            documents, available_tokens=2000, query="test"
        )
        
        print(f"Strategy: {info['strategy']}")
        print(f"Documents: {info['total_documents']}")
        
        # Show first 3 and last 3 documents
        if len(optimized_docs) > 6:
            print("First 3 documents:")
            for i in range(3):
                doc = optimized_docs[i]
                score = doc.get("score", 0)
                doc_id = doc["document"]["id"]
                print(f"  {i+1}. {doc_id} (score: {score:.1f})")
            
            print("...")
            
            print("Last 3 documents:")
            for i in range(len(optimized_docs)-3, len(optimized_docs)):
                doc = optimized_docs[i]
                score = doc.get("score", 0)
                doc_id = doc["document"]["id"]
                print(f"  {i+1}. {doc_id} (score: {score:.1f})")


if __name__ == "__main__":
    # Run demonstration
    demo_context_optimization()
