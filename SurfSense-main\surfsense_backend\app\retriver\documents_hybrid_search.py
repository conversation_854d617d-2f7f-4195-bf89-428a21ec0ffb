import re
import unicodedata
from typing import Optional
import logging
from sqlalchemy import select, func, text, or_
from sqlalchemy.orm import joinedload
from app.utils.vietnamese_text_processor import vietnamese_processor
from app.config.vietnamese_config import vietnamese_config

logger = logging.getLogger(__name__)

class DocumentHybridSearchRetriever:
    def __init__(self, db_session):
        """
        Initialize the hybrid search retriever with a database session.

        Args:
            db_session: SQLAlchemy AsyncSession from FastAPI dependency injection
        """
        self.db_session = db_session

    def _preprocess_vietnamese_text(self, text: str) -> str:
        """
        Preprocess Vietnamese text for better search results using the Vietnamese processor.

        Args:
            text: Input text to preprocess

        Returns:
            Preprocessed text optimized for Vietnamese search
        """
        if not text:
            return text

        return vietnamese_processor.clean_text(text)

    def _create_search_variants(self, query_text: str) -> tuple[str, str]:
        """
        Create search variants for Vietnamese text using the Vietnamese processor.

        Args:
            query_text: Original query text

        Returns:
            Tuple of (primary_variant, unaccented_variant)
        """
        if not query_text:
            return query_text, query_text

        variants = vietnamese_processor.create_search_variants(query_text)

        if len(variants) >= 2:
            # Return the first cleaned variant and the unaccented version
            primary = variants[0]
            unaccented = variants[-1]  # Usually the unaccented version is last
            return primary, unaccented
        elif len(variants) == 1:
            # If only one variant, create unaccented version manually
            primary = variants[0]
            unaccented = vietnamese_processor.remove_diacritics(primary)
            return primary, unaccented
        else:
            # Fallback to original
            return query_text, query_text

    def _get_text_search_config(self) -> str:
        """
        Determine the appropriate PostgreSQL text search configuration.
        Uses Vietnamese configuration settings.

        Returns:
            PostgreSQL text search configuration name
        """
        if vietnamese_config.should_use_vietnamese_optimization():
            return vietnamese_config.get_text_search_config()
        return 'english'  # Fallback to English if Vietnamese optimization is disabled

    async def vector_search(self, query_text: str, top_k: int, user_id: str, search_space_id: int = None) -> list:
        """
        Perform vector similarity search on documents.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            
        Returns:
            List of documents sorted by vector similarity
        """
        from app.db import Document, SearchSpace
        from app.config import config
        
        # Preprocess query for better embedding
        processed_query, _ = self._create_search_variants(query_text)

        # Get embedding for the processed query
        embedding_model = config.embedding_model_instance
        query_embedding = embedding_model.embed(processed_query)
        
        # Build the base query with user ownership check
        query = (
            select(Document)
            .options(joinedload(Document.search_space))
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
        )
        
        # Add search space filter if provided
        if search_space_id is not None:
            query = query.where(Document.search_space_id == search_space_id)
        
        # Add vector similarity ordering
        query = (
            query
            .order_by(Document.embedding.op("<=>")(query_embedding))
            .limit(top_k)
        )
        
        # Execute the query with proper error handling
        try:
            result = await self.db_session.execute(query)
            documents = result.scalars().all()
            return documents
        except Exception as e:
            print(f"Error in vector_search: {e}")
            return []

    async def full_text_search(self, query_text: str, top_k: int, user_id: str, search_space_id: int = None) -> list:
        """
        Perform full-text keyword search on documents with Vietnamese optimization.

        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results

        Returns:
            List of documents sorted by text relevance
        """
        from app.db import Document, SearchSpace

        # Get text search configuration
        search_config = self._get_text_search_config()

        # Preprocess query for Vietnamese
        processed_query, unaccented_query = self._create_search_variants(query_text)

        # Create multiple search strategies for better Vietnamese support
        tsvector = func.to_tsvector(search_config, Document.content)

        # Primary search with processed query
        tsquery_main = func.plainto_tsquery(search_config, processed_query)

        # Fallback searches for better recall
        conditions = [tsvector.op("@@")(tsquery_main)]

        # Add ILIKE search for partial matches (good for Vietnamese)
        if len(processed_query.strip()) > 2:
            conditions.append(Document.content.ilike(f'%{processed_query}%'))

        # Add unaccented search if different from original
        if unaccented_query != processed_query and len(unaccented_query.strip()) > 2:
            conditions.append(Document.content.ilike(f'%{unaccented_query}%'))
        
        # Build the base query with user ownership check
        query = (
            select(Document)
            .options(joinedload(Document.search_space))
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
            .where(or_(*conditions))  # Use OR condition for multiple search strategies
        )

        # Add search space filter if provided
        if search_space_id is not None:
            query = query.where(Document.search_space_id == search_space_id)

        # Add text search ranking with fallback scoring
        # Use ts_rank_cd for full-text matches, and simple relevance for ILIKE matches
        rank_expression = func.coalesce(
            func.ts_rank_cd(tsvector, tsquery_main),
            # Fallback scoring based on content length and match position
            func.case(
                (Document.content.ilike(f'%{processed_query}%'), 0.5),
                else_=0.1
            )
        )

        query = (
            query
            .order_by(rank_expression.desc())
            .limit(top_k)
        )
        
        # Execute the query with proper error handling
        try:
            result = await self.db_session.execute(query)
            documents = result.scalars().all()
            return documents
        except Exception as e:
            logger.error(f"Error in full_text_search: {e}")
            return []

    async def hybrid_search(self, query_text: str, top_k: int, user_id: str, search_space_id: int = None, document_type: str = None) -> list:
        """
        Combine vector similarity and full-text search results using Reciprocal Rank Fusion.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            document_type: Optional document type to filter results (e.g., "FILE", "CRAWLED_URL")
            
        """
        from app.db import Document, SearchSpace, DocumentType
        from app.config import config
        
        # Preprocess query for Vietnamese optimization
        processed_query, unaccented_query = self._create_search_variants(query_text)

        # Get embedding for the processed query
        embedding_model = config.embedding_model_instance
        query_embedding = embedding_model.embed(processed_query)

        # Constants for RRF calculation
        k = 60  # Constant for RRF calculation
        n_results = top_k * 2  # Get more results for better fusion

        # Get text search configuration for Vietnamese
        search_config = self._get_text_search_config()

        # Create tsvector and tsquery for PostgreSQL full-text search with Vietnamese optimization
        tsvector = func.to_tsvector(search_config, Document.content)
        tsquery_main = func.plainto_tsquery(search_config, processed_query)

        # Create additional search conditions for better Vietnamese recall
        text_search_conditions = [tsvector.op("@@")(tsquery_main)]

        # Add ILIKE conditions for partial matching
        if len(processed_query.strip()) > 2:
            text_search_conditions.append(Document.content.ilike(f'%{processed_query}%'))

        if unaccented_query != processed_query and len(unaccented_query.strip()) > 2:
            text_search_conditions.append(Document.content.ilike(f'%{unaccented_query}%'))
        
        # Base conditions for document filtering
        base_conditions = [SearchSpace.user_id == user_id]
        
        # Add search space filter if provided
        if search_space_id is not None:
            base_conditions.append(Document.search_space_id == search_space_id)
            
        # Add document type filter if provided
        if document_type is not None:
            # Convert string to enum value if needed
            if isinstance(document_type, str):
                try:
                    doc_type_enum = DocumentType[document_type]
                    base_conditions.append(Document.document_type == doc_type_enum)
                except KeyError:
                    # If the document type doesn't exist in the enum, return empty results
                    return []
            else:
                base_conditions.append(Document.document_type == document_type)
        
        # CTE for semantic search with user ownership check
        semantic_search_cte = (
            select(
                Document.id,
                func.rank().over(order_by=Document.embedding.op("<=>")(query_embedding)).label("rank")
            )
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(*base_conditions)
        )
        
        semantic_search_cte = (
            semantic_search_cte
            .order_by(Document.embedding.op("<=>")(query_embedding))
            .limit(n_results)
            .cte("semantic_search")
        )
        
        # CTE for keyword search with user ownership check and Vietnamese optimization
        keyword_search_cte = (
            select(
                Document.id,
                func.rank().over(
                    order_by=func.coalesce(
                        func.ts_rank_cd(tsvector, tsquery_main),
                        func.case(
                            (Document.content.ilike(f'%{processed_query}%'), 0.5),
                            else_=0.1
                        )
                    ).desc()
                ).label("rank")
            )
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(*base_conditions)
            .where(or_(*text_search_conditions))
        )

        keyword_search_cte = (
            keyword_search_cte
            .order_by(
                func.coalesce(
                    func.ts_rank_cd(tsvector, tsquery_main),
                    func.case(
                        (Document.content.ilike(f'%{processed_query}%'), 0.5),
                        else_=0.1
                    )
                ).desc()
            )
            .limit(n_results)
            .cte("keyword_search")
        )
        
        # Final combined query using a FULL OUTER JOIN with RRF scoring
        final_query = (
            select(
                Document,
                (
                    func.coalesce(1.0 / (k + semantic_search_cte.c.rank), 0.0) +
                    func.coalesce(1.0 / (k + keyword_search_cte.c.rank), 0.0)
                ).label("score")
            )
            .select_from(
                semantic_search_cte.outerjoin(
                    keyword_search_cte, 
                    semantic_search_cte.c.id == keyword_search_cte.c.id,
                    full=True
                )
            )
            .join(
                Document,
                Document.id == func.coalesce(semantic_search_cte.c.id, keyword_search_cte.c.id)
            )
            .options(joinedload(Document.search_space))
            .order_by(text("score DESC"))
            .limit(top_k)
        )
        
        # Execute the query with proper error handling
        try:
            result = await self.db_session.execute(final_query)
            documents_with_scores = result.all()

            # If no results were found, return an empty list
            if not documents_with_scores:
                return []
        except Exception as e:
            logger.error(f"Error executing hybrid search query: {e}")
            return []
        
        # Convert to serializable dictionaries
        serialized_results = []
        for document, score in documents_with_scores:
            # Fetch associated chunks for this document
            from app.db import Chunk
            
            try:
                chunks_query = select(Chunk).where(Chunk.document_id == document.id).order_by(Chunk.id)
                chunks_result = await self.db_session.execute(chunks_query)
                chunks = chunks_result.scalars().all()
            except Exception as e:
                logger.error(f"Error fetching chunks for document {document.id}: {e}")
                chunks = []
            
            # Concatenate chunks content
            concatenated_chunks_content = " ".join([chunk.content for chunk in chunks]) if chunks else document.content
            
            serialized_results.append({
                "document_id": document.id,
                "title": document.title,
                "content": document.content,
                "chunks_content": concatenated_chunks_content,
                "document_type": document.document_type.value if hasattr(document, 'document_type') else None,
                "metadata": document.document_metadata,
                "score": float(score),  # Ensure score is a Python float
                "search_space_id": document.search_space_id
            })
        
        return serialized_results 