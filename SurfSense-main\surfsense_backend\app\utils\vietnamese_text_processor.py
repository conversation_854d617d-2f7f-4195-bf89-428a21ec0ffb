"""
Vietnamese Text Processing Utilities for Enhanced Search

This module provides utilities for preprocessing Vietnamese text to improve
search accuracy and recall in the SurfSense application.
"""

import re
import unicodedata
from typing import List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class VietnameseTextProcessor:
    """
    A comprehensive Vietnamese text processor for search optimization.
    """
    
    # Common Vietnamese stop words (basic set)
    VIETNAMESE_STOP_WORDS = {
        'và', 'của', 'có', 'là', 'được', 'một', 'này', 'đó', 'những', 'các',
        'cho', 'từ', 'với', 'về', 'trong', 'trên', 'dưới', 'sau', 'trước',
        'khi', 'nếu', 'mà', 'để', 'sẽ', 'đã', 'đang', 'bị', 'bởi', 'theo',
        'như', 'tại', 'lại', 'còn', 'chỉ', 'cũng', 'thì', 'nên', 'phải',
        'không', 'chưa', 'đều', 'cả', 'mọi', 'nhiều', 'ít', 'rất', 'khá',
        'hơn', 'nhất', 'cùng', 'giữa', 'ngoài', 'trong', 'ngoài'
    }
    
    # Vietnamese diacritics mapping for normalization
    DIACRITIC_MAP = {
        'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'
    }
    
    def __init__(self):
        """Initialize the Vietnamese text processor."""
        pass
    
    def normalize_unicode(self, text: str) -> str:
        """
        Normalize Unicode representation of Vietnamese text.
        
        Args:
            text: Input text to normalize
            
        Returns:
            Unicode-normalized text
        """
        if not text:
            return text
        
        # Normalize to NFC (Canonical Decomposition, followed by Canonical Composition)
        return unicodedata.normalize('NFC', text)
    
    def remove_diacritics(self, text: str) -> str:
        """
        Remove Vietnamese diacritics from text for fuzzy matching.
        
        Args:
            text: Input text with diacritics
            
        Returns:
            Text without diacritics
        """
        if not text:
            return text
        
        # Use the diacritic map for more accurate conversion
        result = text.lower()
        for accented, base in self.DIACRITIC_MAP.items():
            result = result.replace(accented, base)
            result = result.replace(accented.upper(), base.upper())
        
        return result
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize Vietnamese text.
        
        Args:
            text: Input text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return text
        
        # Normalize Unicode
        text = self.normalize_unicode(text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep Vietnamese characters
        text = re.sub(r'[^\w\sàáảãạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệìíỉĩịòóỏõọôồốổỗộơờớởỡợùúủũụưừứửữựỳýỷỹỵđ]', ' ', text, flags=re.IGNORECASE)
        
        # Remove extra whitespace again
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def create_search_variants(self, query: str) -> List[str]:
        """
        Create multiple variants of a search query for better matching.
        
        Args:
            query: Original search query
            
        Returns:
            List of query variants
        """
        if not query:
            return [query]
        
        variants = []
        
        # Original cleaned query
        cleaned = self.clean_text(query)
        if cleaned:
            variants.append(cleaned)
        
        # Lowercase version
        lower_cleaned = cleaned.lower()
        if lower_cleaned and lower_cleaned not in variants:
            variants.append(lower_cleaned)
        
        # Version without diacritics
        no_diacritics = self.remove_diacritics(lower_cleaned)
        if no_diacritics and no_diacritics not in variants:
            variants.append(no_diacritics)
        
        # Remove duplicates while preserving order
        unique_variants = []
        for variant in variants:
            if variant and variant not in unique_variants:
                unique_variants.append(variant)
        
        return unique_variants
    
    def extract_keywords(self, text: str, min_length: int = 2) -> List[str]:
        """
        Extract meaningful keywords from Vietnamese text.
        
        Args:
            text: Input text
            min_length: Minimum length of keywords
            
        Returns:
            List of extracted keywords
        """
        if not text:
            return []
        
        # Clean the text
        cleaned = self.clean_text(text)
        
        # Split into words
        words = cleaned.lower().split()
        
        # Filter out stop words and short words
        keywords = [
            word for word in words 
            if len(word) >= min_length and word not in self.VIETNAMESE_STOP_WORDS
        ]
        
        return keywords
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two Vietnamese texts.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score between 0 and 1
        """
        if not text1 or not text2:
            return 0.0
        
        # Extract keywords from both texts
        keywords1 = set(self.extract_keywords(text1))
        keywords2 = set(self.extract_keywords(text2))
        
        if not keywords1 or not keywords2:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        return intersection / union if union > 0 else 0.0
    
    def suggest_corrections(self, query: str) -> List[str]:
        """
        Suggest potential corrections for Vietnamese queries.
        
        Args:
            query: Original query
            
        Returns:
            List of suggested corrections
        """
        suggestions = []
        
        # Add version with proper diacritics (this would need a dictionary in practice)
        # For now, just return the variants
        variants = self.create_search_variants(query)
        
        return variants


# Global instance for easy import
vietnamese_processor = VietnameseTextProcessor()
