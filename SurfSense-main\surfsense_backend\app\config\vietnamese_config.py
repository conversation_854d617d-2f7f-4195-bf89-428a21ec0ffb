"""
Vietnamese-specific configuration for SurfSense

This module contains configuration settings optimized for Vietnamese language processing.
"""

import os
from typing import Dict, List, Optional

class VietnameseConfig:
    """
    Configuration class for Vietnamese language optimization.
    """
    
    # Recommended embedding models for Vietnamese
    VIETNAMESE_EMBEDDING_MODELS = {
        # Multilingual models that work well with Vietnamese
        "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2": {
            "dimension": 384,
            "description": "Good multilingual model, supports Vietnamese well",
            "max_seq_length": 512
        },
        "sentence-transformers/paraphrase-multilingual-mpnet-base-v2": {
            "dimension": 768,
            "description": "High-quality multilingual model, excellent for Vietnamese",
            "max_seq_length": 512
        },
        "intfloat/multilingual-e5-large": {
            "dimension": 1024,
            "description": "State-of-the-art multilingual embedding model",
            "max_seq_length": 512
        },
        "intfloat/multilingual-e5-base": {
            "dimension": 768,
            "description": "Good balance of performance and speed for Vietnamese",
            "max_seq_length": 512
        },
        "mixedbread-ai/mxbai-embed-large-v1": {
            "dimension": 1024,
            "description": "Current default, good multilingual support",
            "max_seq_length": 512
        }
    }
    
    # PostgreSQL text search configurations for Vietnamese
    TEXT_SEARCH_CONFIGS = {
        "simple": {
            "description": "Simple configuration, works well for Vietnamese",
            "pros": ["No stemming", "Preserves Vietnamese words", "Fast"],
            "cons": ["Less sophisticated than language-specific configs"]
        },
        "english": {
            "description": "English configuration (current default)",
            "pros": ["Good for mixed content"],
            "cons": ["Not optimized for Vietnamese", "May miss Vietnamese-specific patterns"]
        }
    }
    
    # Search optimization settings
    SEARCH_SETTINGS = {
        "use_trigram_matching": True,
        "trigram_similarity_threshold": 0.3,
        "enable_fuzzy_search": True,
        "max_edit_distance": 2,
        "min_query_length_for_fuzzy": 3,
        "boost_exact_matches": True,
        "exact_match_boost_factor": 2.0
    }
    
    # Vietnamese-specific text processing settings
    TEXT_PROCESSING = {
        "normalize_unicode": True,
        "remove_extra_whitespace": True,
        "preserve_diacritics_in_primary_search": True,
        "create_unaccented_variants": True,
        "min_keyword_length": 2,
        "enable_stop_word_filtering": True
    }
    
    @classmethod
    def get_recommended_embedding_model(cls) -> str:
        """
        Get the recommended embedding model for Vietnamese.
        
        Returns:
            Model name string
        """
        # Check if current model is already Vietnamese-optimized
        current_model = os.getenv("EMBEDDING_MODEL", "mixedbread-ai/mxbai-embed-large-v1")
        
        if current_model in cls.VIETNAMESE_EMBEDDING_MODELS:
            return current_model
        
        # Return the best balance of performance and compatibility
        return "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_text_search_config(cls) -> str:
        """
        Get the recommended PostgreSQL text search configuration for Vietnamese.
        
        Returns:
            Configuration name
        """
        return "simple"
    
    @classmethod
    def get_search_optimization_settings(cls) -> Dict:
        """
        Get search optimization settings for Vietnamese.
        
        Returns:
            Dictionary of optimization settings
        """
        return cls.SEARCH_SETTINGS.copy()
    
    @classmethod
    def should_use_vietnamese_optimization(cls) -> bool:
        """
        Determine if Vietnamese optimization should be enabled.
        
        Returns:
            True if Vietnamese optimization should be used
        """
        # Check environment variable
        return os.getenv("ENABLE_VIETNAMESE_OPTIMIZATION", "true").lower() == "true"
    
    @classmethod
    def get_chunking_strategy(cls) -> Dict:
        """
        Get recommended chunking strategy for Vietnamese text.
        
        Returns:
            Chunking configuration
        """
        return {
            "chunk_size": 400,  # Slightly smaller for Vietnamese due to word density
            "chunk_overlap": 50,
            "respect_sentence_boundaries": True,
            "vietnamese_sentence_endings": [".", "!", "?", "…", "。"],
            "preserve_paragraphs": True
        }
    
    @classmethod
    def get_reranking_settings(cls) -> Dict:
        """
        Get reranking settings optimized for Vietnamese.
        
        Returns:
            Reranking configuration
        """
        return {
            "enable_semantic_reranking": True,
            "enable_keyword_boost": True,
            "keyword_boost_factor": 1.5,
            "title_match_boost": 2.0,
            "exact_phrase_boost": 3.0,
            "vietnamese_phrase_detection": True
        }


# Environment-based configuration
def get_vietnamese_config() -> VietnameseConfig:
    """
    Get Vietnamese configuration instance.
    
    Returns:
        VietnameseConfig instance
    """
    return VietnameseConfig()


# Export for easy import
vietnamese_config = get_vietnamese_config()
