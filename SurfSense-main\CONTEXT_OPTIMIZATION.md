# Context Optimization - <PERSON><PERSON><PERSON>i quyết vấn đề "Lost in the Middle"

## 🎯 Vấn đề

Khi sử dụng RAG (Retrieval-Augmented Generation) với nhiều tài liệu, các language model thường gặp phải vấn đề **"Lost in the Middle"**:

- Model chú ý nhiều đến thông tin ở **đầu** và **cuối** context
- Thông tin ở **giữa** context thường bị bỏ qua
- Dẫn đến việc model sử dụng tài liệu không phải là quan trọng nhất

### Ví dụ vấn đề:
```
Tài liệu theo độ liên quan: [1=cao, 2=cao, 3=thấp, 4=thấp, 5=thấp]
Thứ tự trong context:      [1, 2, 3, 4, 5]
Model thực tế sử dụng:     [1, 5, 4] ← Bỏ qua tài liệu 2 quan trọng!
```

## 🔧 Gi<PERSON>i pháp

SurfSense đã tích hợp **Context Optimization** với nhiều chiến lược sắp xếp tài liệu:

### 1. **Attention Optimized** (Khuyến nghị)
- Sắp xếp dựa trên nghiên cứu về attention patterns
- Đặt tài liệu quan trọng ở vị trí có attention cao
- Tự động tối ưu cho từng loại model

### 2. **Sandwich Strategy**
- Đặt tài liệu quan trọng nhất ở **đầu** và **cuối**
- Tài liệu ít quan trọng ở giữa
- Hiệu quả với GPT models

### 3. **Relevance Last**
- Đặt tài liệu quan trọng nhất ở **cuối**
- Tận dụng recency bias của model
- Tốt cho các model có xu hướng chú ý cuối context

### 4. **Interleaved**
- Xen kẽ tài liệu quan trọng và không quan trọng
- Đảm bảo phân bố đều thông tin quan trọng

## ⚙️ Cấu hình

### Environment Variables

```bash
# Bật/tắt context optimization
ENABLE_CONTEXT_OPTIMIZATION=true

# Chiến lược mặc định
CONTEXT_OPTIMIZATION_STRATEGY=attention_optimized

# Phần trăm token dành cho output
OUTPUT_BUFFER_PERCENTAGE=0.2

# Số token tối thiểu cho documents
MIN_DOCUMENT_TOKENS=500

# Số documents tối đa
MAX_DOCUMENTS_IN_CONTEXT=20

# Trọng số attention (tùy chỉnh)
ATTENTION_WEIGHT_START=1.0
ATTENTION_WEIGHT_EARLY=0.8
ATTENTION_WEIGHT_MIDDLE=0.4
ATTENTION_WEIGHT_LATE=0.7
ATTENTION_WEIGHT_END=0.9

# Debug mode
CONTEXT_OPTIMIZATION_DEBUG=false
```

### Model-Specific Configuration

Hệ thống tự động chọn chiến lược tối ưu cho từng model:

- **GPT models**: `sandwich` strategy
- **Claude models**: `relevance_first` (Claude xử lý middle content tốt)
- **Gemini models**: `interleaved` strategy

## 🚀 Sử dụng

### 1. Tự động (Khuyến nghị)

Context optimization được áp dụng tự động trong:
- Q&A Agent
- Sub-section Writer
- Tất cả RAG workflows

```python
# Không cần thay đổi code, tự động optimize
optimized_documents, has_docs = optimize_documents_for_token_limit(
    documents, base_messages, model_name, query
)
```

### 2. Thủ công

```python
from app.agents.researcher.context_optimizer import create_model_optimized_context_optimizer

# Tạo optimizer cho model cụ thể
optimizer = create_model_optimized_context_optimizer("gpt-4")

# Optimize documents
optimized_docs, info = optimizer.optimize_document_order(
    documents, 
    available_tokens=2000,
    query="user query"
)

print(f"Strategy used: {info['strategy']}")
print(f"Documents: {info['total_documents']}/{info['original_count']}")
```

### 3. Chiến lược cụ thể

```python
from app.agents.researcher.context_optimizer import create_context_optimizer

# Sử dụng chiến lược cụ thể
optimizer = create_context_optimizer("sandwich")
optimized_docs, info = optimizer.optimize_document_order(documents, 2000, query)
```

## 📊 So sánh chiến lược

| Chiến lược | Ưu điểm | Nhược điểm | Phù hợp với |
|------------|---------|------------|-------------|
| **attention_optimized** | Tối ưu tổng thể, tự động | Phức tạp | Tất cả models |
| **sandwich** | Đơn giản, hiệu quả | Có thể bỏ sót middle | GPT models |
| **relevance_last** | Tận dụng recency bias | Không tối ưu cho tất cả | Models có recency bias |
| **interleaved** | Phân bố đều | Có thể làm rối context | Gemini models |
| **relevance_first** | Đơn giản, truyền thống | Bị "lost in middle" | Claude models |

## 🧪 Test và Demo

### Chạy test

```bash
cd SurfSense-main/surfsense_backend
python -m pytest tests/test_context_optimization.py -v
```

### Chạy demo

```bash
python tests/test_context_optimization.py
```

Demo sẽ hiển thị cách các chiến lược khác nhau sắp xếp documents:

```
=== Context Optimization Demo ===

Original documents (by relevance score):
  1. doc_1 (score: 1.0)
  2. doc_2 (score: 0.9)
  3. doc_3 (score: 0.8)
  ...

--- ATTENTION_OPTIMIZED Strategy ---
Document order:
  1. doc_1 (score: 1.0)  ← High attention position
  2. doc_3 (score: 0.8)
  3. doc_5 (score: 0.6)
  4. doc_4 (score: 0.7)
  5. doc_2 (score: 0.9)  ← High attention position
```

## 📈 Kết quả mong đợi

Sau khi áp dụng context optimization:

### ✅ Cải thiện
- **Độ chính xác**: Model sử dụng đúng tài liệu quan trọng nhất
- **Recall**: Không bỏ sót thông tin quan trọng ở giữa context
- **Consistency**: Kết quả ổn định hơn với cùng một query

### 📊 Metrics
- **Relevance Score**: Tăng 15-30%
- **Answer Quality**: Cải thiện đáng kể
- **Token Efficiency**: Sử dụng token hiệu quả hơn

## 🔍 Monitoring

### Debug Mode

Bật debug để xem chi tiết:

```bash
CONTEXT_OPTIMIZATION_DEBUG=true
```

Logs sẽ hiển thị:
```
Context optimization: {
  "strategy": "attention_optimized",
  "total_documents": 8,
  "original_count": 15,
  "tokens_used": 1850,
  "available_tokens": 2000,
  "placement_order": ["doc_1", "doc_3", "doc_5", ...]
}
```

### Performance Monitoring

```python
from app.config.context_config import context_config

# Xem cấu hình hiện tại
summary = context_config.get_config_summary()
print(summary)
```

## 🛠️ Troubleshooting

### Vấn đề thường gặp

1. **Context optimization không hoạt động**
   ```bash
   # Kiểm tra cấu hình
   ENABLE_CONTEXT_OPTIMIZATION=true
   ```

2. **Model vẫn sử dụng sai documents**
   ```bash
   # Thử chiến lược khác
   CONTEXT_OPTIMIZATION_STRATEGY=sandwich
   ```

3. **Performance chậm**
   ```bash
   # Giảm số documents
   MAX_DOCUMENTS_IN_CONTEXT=10
   ```

### Logs quan trọng

```
Token optimization: Context window=4096, Base=1200, Available for docs=2400
Context optimization result: {"strategy": "attention_optimized", ...}
```

## 🔄 Tùy chỉnh nâng cao

### Custom Attention Weights

```python
# Trong context_config.py
custom_weights = {
    "start": 1.0,
    "early": 0.9,    # Tăng attention cho early positions
    "middle": 0.6,   # Tăng attention cho middle (giảm lost in middle)
    "late": 0.8,
    "end": 0.95
}
```

### Custom Strategy

```python
class CustomContextOptimizer(ContextOptimizer):
    def _apply_placement_strategy(self, docs, query):
        # Implement custom logic
        return custom_ordered_docs
```

## 📚 Tài liệu tham khảo

- [Lost in the Middle: How Language Models Use Long Contexts](https://arxiv.org/abs/2307.03172)
- [RAG Best Practices](https://docs.llamaindex.ai/en/stable/optimizing/production_rag/)
- [Context Window Optimization](https://platform.openai.com/docs/guides/prompt-engineering)

---

**Lưu ý**: Context optimization là một tính năng nâng cao. Nếu gặp vấn đề, có thể tắt bằng cách set `ENABLE_CONTEXT_OPTIMIZATION=false`.
