"""
Tests for Vietnamese search optimization

This module contains tests to verify that Vietnamese search optimizations
are working correctly.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from app.utils.vietnamese_text_processor import vietnamese_processor
from app.config.vietnamese_config import vietnamese_config
from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever


class TestVietnameseTextProcessor:
    """Test Vietnamese text processing utilities."""
    
    def test_normalize_unicode(self):
        """Test Unicode normalization."""
        # Test with Vietnamese text containing various Unicode forms
        text = "Việt Nam"
        normalized = vietnamese_processor.normalize_unicode(text)
        assert normalized == "Việt Nam"
        assert len(normalized) > 0
    
    def test_remove_diacritics(self):
        """Test diacritic removal."""
        test_cases = [
            ("Việt Nam", "viet nam"),
            ("công nghệ", "cong nghe"),
            ("thông tin", "thong tin"),
            ("phần mềm", "phan mem"),
            ("đại học", "dai hoc")
        ]
        
        for original, expected in test_cases:
            result = vietnamese_processor.remove_diacritics(original)
            assert result == expected, f"Expected '{expected}', got '{result}'"
    
    def test_clean_text(self):
        """Test text cleaning."""
        test_cases = [
            ("  Việt   Nam  ", "Việt Nam"),
            ("Công nghệ!!! thông tin???", "Công nghệ thông tin"),
            ("Phần@mềm#máy$tính", "Phần mềm máy tính"),
            ("", ""),
            (None, None)
        ]
        
        for original, expected in test_cases:
            result = vietnamese_processor.clean_text(original)
            assert result == expected, f"Expected '{expected}', got '{result}'"
    
    def test_create_search_variants(self):
        """Test search variant creation."""
        query = "Công nghệ thông tin"
        variants = vietnamese_processor.create_search_variants(query)
        
        assert len(variants) > 0
        assert any("công nghệ thông tin" in variant.lower() for variant in variants)
        assert any("cong nghe thong tin" in variant.lower() for variant in variants)
    
    def test_extract_keywords(self):
        """Test keyword extraction."""
        text = "Công nghệ thông tin là một lĩnh vực rất quan trọng trong thời đại hiện tại"
        keywords = vietnamese_processor.extract_keywords(text)
        
        # Should extract meaningful keywords and filter out stop words
        assert "công" in keywords or "công nghệ" in " ".join(keywords)
        assert "thông" in keywords or "thông tin" in " ".join(keywords)
        assert "lĩnh" in keywords or "lĩnh vực" in " ".join(keywords)
        
        # Should filter out stop words
        assert "là" not in keywords
        assert "một" not in keywords
        assert "trong" not in keywords
    
    def test_calculate_text_similarity(self):
        """Test text similarity calculation."""
        text1 = "Công nghệ thông tin"
        text2 = "Công nghệ máy tính"
        text3 = "Kinh tế học"
        
        # Similar texts should have higher similarity
        sim1 = vietnamese_processor.calculate_text_similarity(text1, text2)
        sim2 = vietnamese_processor.calculate_text_similarity(text1, text3)
        
        assert sim1 > sim2
        assert 0 <= sim1 <= 1
        assert 0 <= sim2 <= 1


class TestVietnameseConfig:
    """Test Vietnamese configuration."""
    
    def test_get_recommended_embedding_model(self):
        """Test embedding model recommendation."""
        model = vietnamese_config.get_recommended_embedding_model()
        assert model in vietnamese_config.VIETNAMESE_EMBEDDING_MODELS
    
    def test_get_text_search_config(self):
        """Test text search configuration."""
        config = vietnamese_config.get_text_search_config()
        assert config in vietnamese_config.TEXT_SEARCH_CONFIGS
    
    def test_get_search_optimization_settings(self):
        """Test search optimization settings."""
        settings = vietnamese_config.get_search_optimization_settings()
        assert isinstance(settings, dict)
        assert "use_trigram_matching" in settings
        assert "enable_fuzzy_search" in settings
    
    def test_get_chunking_strategy(self):
        """Test chunking strategy."""
        strategy = vietnamese_config.get_chunking_strategy()
        assert isinstance(strategy, dict)
        assert "chunk_size" in strategy
        assert "vietnamese_sentence_endings" in strategy


class TestDocumentHybridSearchRetriever:
    """Test Vietnamese-optimized hybrid search retriever."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return AsyncMock()
    
    @pytest.fixture
    def retriever(self, mock_db_session):
        """Create a retriever instance with mock session."""
        return DocumentHybridSearchRetriever(mock_db_session)
    
    def test_preprocess_vietnamese_text(self, retriever):
        """Test Vietnamese text preprocessing."""
        test_cases = [
            ("Công nghệ thông tin", "Công nghệ thông tin"),
            ("  Việt   Nam  ", "Việt Nam"),
            ("", ""),
            (None, None)
        ]
        
        for original, expected in test_cases:
            result = retriever._preprocess_vietnamese_text(original)
            if expected is None:
                assert result == original
            else:
                assert result == expected
    
    def test_create_search_variants(self, retriever):
        """Test search variant creation in retriever."""
        query = "Công nghệ thông tin"
        primary, unaccented = retriever._create_search_variants(query)
        
        assert primary is not None
        assert unaccented is not None
        assert len(primary) > 0
        assert len(unaccented) > 0
        
        # Unaccented version should not have Vietnamese diacritics
        assert "ô" not in unaccented
        assert "ệ" not in unaccented
    
    def test_get_text_search_config(self, retriever):
        """Test text search configuration selection."""
        config = retriever._get_text_search_config()
        assert config in ["simple", "english"]


class TestIntegration:
    """Integration tests for Vietnamese search optimization."""
    
    def test_end_to_end_text_processing(self):
        """Test complete text processing pipeline."""
        original_query = "Tìm kiếm thông tin về công nghệ phần mềm"
        
        # Process through Vietnamese processor
        variants = vietnamese_processor.create_search_variants(original_query)
        assert len(variants) > 0
        
        # Extract keywords
        keywords = vietnamese_processor.extract_keywords(original_query)
        assert len(keywords) > 0
        
        # Check that meaningful keywords are extracted
        meaningful_keywords = ["tìm", "kiếm", "thông", "tin", "công", "nghệ", "phần", "mềm"]
        extracted_words = " ".join(keywords).lower()
        
        # At least some meaningful keywords should be present
        found_keywords = [kw for kw in meaningful_keywords if kw in extracted_words]
        assert len(found_keywords) > 0
    
    def test_search_variant_quality(self):
        """Test quality of search variants."""
        test_queries = [
            "Công nghệ thông tin",
            "Phần mềm máy tính", 
            "Trí tuệ nhân tạo",
            "Khoa học dữ liệu",
            "Học máy"
        ]
        
        for query in test_queries:
            variants = vietnamese_processor.create_search_variants(query)
            
            # Should have at least original and unaccented versions
            assert len(variants) >= 1
            
            # Should contain the original concept
            combined_variants = " ".join(variants).lower()
            original_words = query.lower().split()
            
            # At least some original words should be preserved in variants
            preserved_words = [word for word in original_words if word in combined_variants]
            assert len(preserved_words) > 0


# Utility functions for manual testing
def manual_test_vietnamese_search():
    """
    Manual test function for Vietnamese search.
    Run this to test Vietnamese search functionality interactively.
    """
    print("=== Vietnamese Search Test ===")
    
    # Test text processor
    processor = vietnamese_processor
    
    test_queries = [
        "Công nghệ thông tin",
        "Phần mềm máy tính",
        "Trí tuệ nhân tạo", 
        "cong nghe thong tin",  # Without diacritics
        "phan mem may tinh"     # Without diacritics
    ]
    
    for query in test_queries:
        print(f"\nOriginal query: {query}")
        
        # Clean text
        cleaned = processor.clean_text(query)
        print(f"Cleaned: {cleaned}")
        
        # Create variants
        variants = processor.create_search_variants(query)
        print(f"Variants: {variants}")
        
        # Extract keywords
        keywords = processor.extract_keywords(query)
        print(f"Keywords: {keywords}")
        
        print("-" * 50)


if __name__ == "__main__":
    # Run manual test
    manual_test_vietnamese_search()
