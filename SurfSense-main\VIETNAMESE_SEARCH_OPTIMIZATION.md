# Tối ưu hóa Tìm kiếm Tiếng Việt cho SurfSense

## Tổng quan

Tài liệu này mô tả các cải tiến được thực hiện để tối ưu hóa khả năng tìm kiếm tiếng Việt trong SurfSense. Các tối ưu hóa này bao gồm xử lý văn bản tiếng Việt, cải thiện full-text search, và tối ưu hóa vector embeddings.

## Các cải tiến chính

### 1. Xử lý văn bản tiếng Việt (Vietnamese Text Processing)

**File:** `app/utils/vietnamese_text_processor.py`

- **Unicode normalization**: Chuẩn hóa các ký tự Unicode tiếng Việt
- **Diacritic handling**: Xử lý dấu thanh tiếng Việt cho tìm kiếm mờ (fuzzy search)
- **Stop words filtering**: <PERSON><PERSON><PERSON> các từ dừng tiếng Việt
- **Keyword extraction**: Tr<PERSON><PERSON> xuất từ khóa có ý nghĩa từ văn bản tiếng Việt

### 2. Cải thiện Full-text Search

**File:** `app/retriver/documents_hybrid_search.py`

- **PostgreSQL configuration**: Sử dụng `simple` config thay vì `english` cho tiếng Việt
- **Multiple search strategies**: Kết hợp full-text search với ILIKE pattern matching
- **Search variants**: Tạo nhiều biến thể của query để tăng recall
- **Ranking optimization**: Cải thiện thuật toán xếp hạng cho tiếng Việt

### 3. Database Indexes tối ưu

**File:** `app/db.py` và `alembic/versions/vietnamese_search_optimization.py`

- **Trigram indexes**: Sử dụng pg_trgm extension cho partial matching
- **Simple text search indexes**: Indexes tối ưu cho tiếng Việt
- **Composite indexes**: Indexes kết hợp cho các pattern tìm kiếm phổ biến

### 4. Cấu hình Vietnamese

**File:** `app/config/vietnamese_config.py`

- **Embedding models**: Danh sách các model embedding tốt cho tiếng Việt
- **Search settings**: Các tham số tối ưu cho tìm kiếm tiếng Việt
- **Text processing settings**: Cấu hình xử lý văn bản tiếng Việt

## Cách sử dụng

### 1. Kích hoạt tối ưu hóa tiếng Việt

Thêm vào file `.env`:

```bash
# Kích hoạt tối ưu hóa tiếng Việt
ENABLE_VIETNAMESE_OPTIMIZATION=true

# Sử dụng embedding model tốt cho tiếng Việt
EMBEDDING_MODEL=intfloat/multilingual-e5-base
```

### 2. Chạy migration để tạo indexes

```bash
cd surfsense_backend
alembic upgrade head
```

### 3. Khởi động lại ứng dụng

```bash
# Nếu sử dụng Docker
docker-compose restart backend

# Hoặc chạy trực tiếp
python main.py
```

## Embedding Models được khuyến nghị

### Cho hiệu suất tốt nhất:
- `intfloat/multilingual-e5-large` (1024 dimensions)
- `sentence-transformers/paraphrase-multilingual-mpnet-base-v2` (768 dimensions)

### Cho cân bằng hiệu suất/tốc độ:
- `intfloat/multilingual-e5-base` (768 dimensions)
- `sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2` (384 dimensions)

## Các tính năng tìm kiếm mới

### 1. Tìm kiếm mờ (Fuzzy Search)
- Tự động tạo các biến thể không dấu của query
- Hỗ trợ tìm kiếm khi người dùng gõ thiếu dấu

### 2. Tìm kiếm từng phần (Partial Matching)
- Sử dụng trigram matching cho các từ không hoàn chỉnh
- Tốt cho tìm kiếm các từ ghép tiếng Việt

### 3. Tìm kiếm kết hợp (Hybrid Search)
- Kết hợp vector similarity và keyword matching
- Tối ưu hóa Reciprocal Rank Fusion cho tiếng Việt

## Kiểm tra hiệu quả

### 1. Test với các query tiếng Việt:

```python
# Ví dụ test trong Python
from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever

retriever = DocumentHybridSearchRetriever(db_session)

# Test với dấu
results1 = await retriever.hybrid_search("công nghệ thông tin", 10, user_id)

# Test không dấu
results2 = await retriever.hybrid_search("cong nghe thong tin", 10, user_id)

# Test từ khóa một phần
results3 = await retriever.hybrid_search("công nghệ", 10, user_id)
```

### 2. Kiểm tra indexes:

```sql
-- Kiểm tra trigram indexes
SELECT schemaname, tablename, indexname 
FROM pg_indexes 
WHERE indexname LIKE '%trgm%';

-- Kiểm tra simple text search indexes
SELECT schemaname, tablename, indexname 
FROM pg_indexes 
WHERE indexname LIKE '%simple%';
```

## Troubleshooting

### 1. Lỗi pg_trgm extension
```sql
-- Tạo extension thủ công nếu cần
CREATE EXTENSION IF NOT EXISTS pg_trgm;
```

### 2. Hiệu suất chậm
- Kiểm tra indexes đã được tạo chưa
- Xem xét giảm `top_k` trong queries
- Sử dụng embedding model nhỏ hơn nếu cần

### 3. Kết quả không chính xác
- Kiểm tra `ENABLE_VIETNAMESE_OPTIMIZATION=true`
- Xem xét điều chỉnh similarity thresholds
- Kiểm tra text preprocessing

## Monitoring và Metrics

### 1. Query performance
```sql
-- Kiểm tra slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE query LIKE '%to_tsvector%' 
ORDER BY mean_time DESC;
```

### 2. Index usage
```sql
-- Kiểm tra index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE indexname LIKE '%trgm%' OR indexname LIKE '%simple%';
```

## Tương lai

### Các cải tiến có thể thêm:
1. **Vietnamese word segmentation**: Tách từ tiếng Việt chính xác hơn
2. **Semantic search**: Tìm kiếm theo nghĩa, không chỉ từ khóa
3. **Query expansion**: Mở rộng query với từ đồng nghĩa
4. **Learning to rank**: Học cách xếp hạng từ user feedback
5. **Vietnamese NER**: Nhận diện thực thể có tên tiếng Việt

## Liên hệ

Nếu có vấn đề hoặc đề xuất cải tiến, vui lòng tạo issue trong repository.
