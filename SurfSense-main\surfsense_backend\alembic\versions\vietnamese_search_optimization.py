"""Vietnamese search optimization

Revision ID: vietnamese_search_opt
Revises: e55302644c51
Create Date: 2024-07-04 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'vietnamese_search_opt'
down_revision: Union[str, None] = 'e55302644c51'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Add Vietnamese-optimized search indexes and extensions.
    """
    # Enable pg_trgm extension for trigram matching (good for Vietnamese)
    op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
    
    # Create Vietnamese-optimized indexes for documents
    # Use 'simple' text search configuration which works better for Vietnamese
    op.execute("""
        CREATE INDEX IF NOT EXISTS document_search_index_simple 
        ON documents USING gin (to_tsvector('simple', content))
    """)
    
    # Add trigram index for partial matching (excellent for Vietnamese)
    op.execute("""
        CREATE INDEX IF NOT EXISTS document_content_trgm_index 
        ON documents USING gin (content gin_trgm_ops)
    """)
    
    # Create Vietnamese-optimized indexes for chunks
    op.execute("""
        CREATE INDEX IF NOT EXISTS chunks_search_index_simple 
        ON chunks USING gin (to_tsvector('simple', content))
    """)
    
    # Add trigram index for chunks
    op.execute("""
        CREATE INDEX IF NOT EXISTS chunks_content_trgm_index 
        ON chunks USING gin (content gin_trgm_ops)
    """)
    
    # Add indexes for title field as well (often searched)
    op.execute("""
        CREATE INDEX IF NOT EXISTS document_title_trgm_index 
        ON documents USING gin (title gin_trgm_ops)
    """)
    
    # Create a composite index for common search patterns
    op.execute("""
        CREATE INDEX IF NOT EXISTS document_search_composite_index 
        ON documents (search_space_id, document_type) 
        INCLUDE (title, content)
    """)


def downgrade() -> None:
    """
    Remove Vietnamese-optimized search indexes.
    """
    # Drop the indexes we created
    op.execute("DROP INDEX IF EXISTS document_search_index_simple")
    op.execute("DROP INDEX IF EXISTS document_content_trgm_index")
    op.execute("DROP INDEX IF EXISTS chunks_search_index_simple")
    op.execute("DROP INDEX IF EXISTS chunks_content_trgm_index")
    op.execute("DROP INDEX IF EXISTS document_title_trgm_index")
    op.execute("DROP INDEX IF EXISTS document_search_composite_index")
    
    # Note: We don't drop pg_trgm extension as it might be used by other parts
    # of the application or other databases on the same server
